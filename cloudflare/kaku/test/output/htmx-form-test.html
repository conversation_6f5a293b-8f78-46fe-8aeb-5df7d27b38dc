<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTMX Form Generator Test - 2-Step Verification</title>
    <script src="https://unpkg.com/htmx.org@2.0.4"></script>
    <script src="https://unpkg.com/htmx.org/dist/ext/ws.js"></script>
    <link rel="stylesheet" href="../../public/css/styles.css" />
    <link rel="stylesheet" href="../../public/css/video-layout.css" />
    <link rel="stylesheet" href="../../public/css/bottom-sheet.css" />
</head>
<body class="light bg-neutral-60 dark:bg-primary-50 h-screen flex items-center justify-center">
    <div class="relative md:w-[435px] md:rounded-xl shadow-md shadow-neutral-50 bg-surface">
        <div id="connection-flow-wrapper" class="h-screen flex flex-col justify-center items-center relative md:w-[435px] md:h-auto md:rounded-xl shadow-md shadow-neutral-50 bg-surface">
            <div id="connection-flow" class="w-screen flex flex-col items-center justify-center bg-surface text-surface-on-surface md:h-[auto] md:min-h-[600px] md:w-[435px] md:rounded-xl p-8">
                <div class="text-start w-full">
                    <h1 class="text-xl font-semibold mb-4">2-Step Verification</h1>
                    <p class="text-base mb-2">Google sent a notification to your iPhone. Open the Gmail app, tap Yes on the prompt, then tap 88 on your phone to verify it's you.</p>
                </div>
                <form class="form-container" hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML" hx-vals='{"type": "form_submission"}'>
<div class="prompt-container">
      <div class="verification-code-display">
        <div class="verification-code-label">Verification Code</div>
        <div class="verification-code-value">88</div>
      </div>
    
      <div class="contextual-info">
        <div class="contextual-info-text">Open the Gmail app on iPhone
Google sent a notification to your iPhone. Open the Gmail app, tap Yes on the prompt, then tap 88 on your phone to verify it's you.</div>
      </div>
    </div>

    <div class="input-container hidden">
      <div class="checkbox-container">
        <input
          class="checkbox-field"
          type="checkbox"
          checked
          id="checkbox_dont_ask_again"
          name="Don't ask again on this device"
        >
        <label class="checkbox-label" for="checkbox_dont_ask_again">Don't ask again on this device</label>
      </div>
    </div>
  

    <div class="button-container">
      <button
        type="button"
        class="button-secondary"
        hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML" hx-trigger="click" hx-vals='{"type": "form_submission", "clickId": "button_try_another_way", "interaction": "click"}'
      >
        Try another way
      </button>
    </div>
  
</form>

               <div style="margin-top: 2rem; padding: 1rem; background-color: #f0f0f0; border-radius: 0.5rem; width: 100%;">
  <h3 style="font-size: 0.875rem; font-weight: 600; color: #1c1b1f; margin-bottom: 0.5rem;">
    Form Analysis Data (Simplified Schema):
  </h3>
  <details style="font-size: 0.75rem;">
    <summary style="cursor: pointer; color: #6750a4; margin-bottom: 0.5rem; transition: color 0.2s;"
             onmouseover="this.style.color='#5f4da5'"
             onmouseout="this.style.color='#6750a4'">
      View Raw JSON
    </summary>
    <pre style="margin-top: 0.5rem; padding: 0.5rem; background-color: #ffffff; border: 1px solid #cac4d0; border-radius: 0.25rem; font-size: 0.75rem; overflow: auto; max-height: 10rem; color: #1c1b1f;">
      {
  "extractionResult": {
    "screenInfo": {
      "authState": "not-authenticated",
      "errors": [],
      "title": "2-Step Verification",
      "controlVisibilityRules": [
        {
          "id": "field_redacted",
          "status": "included",
          "reason": "This is a redacted field, likely for username or email."
        },
        {
          "id": "checkbox_dont_ask_again",
          "status": "included",
          "reason": "This control allows the user to remain authenticated or have their device remembered."
        },
        {
          "id": "button_try_another_way",
          "status": "included",
          "reason": "This is an alternative way to authenticate."
        }
      ],
      "description": "To help keep your account safe, Google wants to make sure it's really you trying to sign in",
      "instruction": "Open the Gmail app on iPhone\nGoogle sent a notification to your iPhone. Open the Gmail app, tap Yes on the prompt, then tap 88 on your phone to verify it's you."
    },
    "controls": {
      "fields": [
        {
          "isLikelyDropdownReason": "The control has a dropdown arrow.",
          "id": "field_redacted",
          "order": 1,
          "label": "***REDACTED***",
          "fieldControlType": "dropdown",
          "actiontype": "select",
          "name": "***REDACTED***",
          "checked": false,
          "isDontAskAgainControl": false
        },
        {
          "isLikelyDropdownReason": "The control is a checkbox.",
          "id": "checkbox_dont_ask_again",
          "order": 2,
          "label": "Don't ask again on this device",
          "fieldControlType": "checkbox",
          "actiontype": "select",
          "name": "Don't ask again on this device",
          "checked": true,
          "isDontAskAgainControl": true
        }
      ],
      "buttons": [
        {
          "id": "button_try_another_way",
          "order": 3,
          "label": "Try another way",
          "variant": "link",
          "type": "click",
          "actiontype": "click",
          "isDontAskAgainControl": false
        }
      ]
    }
  },
  "classificationResult": {
    "screenInfo": {
      "classificationReasoning": "The screen displays a prompt for 2-Step Verification, indicating a need for a second factor to confirm identity. It also shows a code '88' to be entered on another device, which is a common pattern for multi-factor authentication.",
      "screenClass": "multi-factor-verification-screen",
      "description": "Google sent a notification to your iPhone. Open the Gmail app, tap Yes on the prompt, then tap 88 on your phone to verify it's you.",
      "title": "2-Step Verification",
      "authState": "not-authenticated",
      "errors": null,
      "verificationCode": "88"
    }
  }
}
    </pre>
  </details>
</div>

            </div>
        </div>
    </div>
</body>
</html>