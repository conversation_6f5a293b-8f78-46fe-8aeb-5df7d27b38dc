<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button & Verification Code Styling Demo</title>
    <link rel="stylesheet" href="../../public/css/styles.css" />
</head>
<body class="light bg-neutral-60 dark:bg-primary-50 h-screen flex items-center justify-center">
    <div class="relative md:w-[435px] md:rounded-xl shadow-md shadow-neutral-50 bg-surface">
        <div class="w-screen flex flex-col items-center justify-center bg-surface text-surface-on-surface md:h-[auto] md:min-h-[600px] md:w-[435px] md:rounded-xl p-8">
            <div class="text-start w-full">
                <h1 class="text-xl font-semibold mb-6">Enhanced Styling Demo</h1>
                <p class="text-base mb-6">Showcasing the improved button-link and verification code styling</p>
            </div>

            <!-- Enhanced Verification Code Display -->
            <div class="prompt-container mb-8">
                <div class="verification-code-display">
                    <div class="verification-code-label">Verification Code</div>
                    <div class="verification-code-value">123456</div>
                </div>
                
                <div class="contextual-info">
                    <div class="contextual-info-text">
                        Check your authenticator app and enter the 6-digit code above. 
                        The code refreshes every 30 seconds for security.
                    </div>
                </div>
            </div>

            <!-- Button Hierarchy Demo -->
            <div class="w-full space-y-4">
                <h2 class="text-lg font-medium mb-4">Button Hierarchy</h2>
                
                <!-- Primary Button (Most Prominent) -->
                <div class="button-container">
                    <button type="button" class="button-primary">
                        Primary Action
                    </button>
                </div>

                <!-- Link Button (More Prominent than Secondary, Less than Primary) -->
                <div class="button-container">
                    <button type="button" class="button-link">
                        Link Action (Enhanced)
                    </button>
                </div>

                <!-- Secondary Button (Least Prominent) -->
                <div class="button-container">
                    <button type="button" class="button-secondary">
                        Secondary Action
                    </button>
                </div>

                <!-- Danger Button (For Reference) -->
                <div class="button-container">
                    <button type="button" class="button-danger">
                        Danger Action
                    </button>
                </div>
            </div>

            <!-- Additional Verification Code Examples -->
            <div class="w-full mt-8">
                <h2 class="text-lg font-medium mb-4">Verification Code Variations</h2>
                
                <div class="space-y-4">
                    <div class="verification-code-display">
                        <div class="verification-code-label">SMS Code</div>
                        <div class="verification-code-value">88</div>
                    </div>

                    <div class="verification-code-display">
                        <div class="verification-code-label">Email Verification</div>
                        <div class="verification-code-value">VERIFY</div>
                    </div>

                    <div class="verification-code-display">
                        <div class="verification-code-label">Backup Code</div>
                        <div class="verification-code-value">ABC-123-XYZ</div>
                    </div>
                </div>
            </div>

            <div class="mt-8 p-4 bg-neutral-95 rounded-lg">
                <h3 class="text-sm font-semibold mb-2">Styling Improvements:</h3>
                <ul class="text-xs space-y-1 text-neutral-30">
                    <li>• Button-link now has subtle background, border, and shadow effects</li>
                    <li>• More prominent than secondary but less than primary buttons</li>
                    <li>• Verification codes feature enhanced gradients and animations</li>
                    <li>• Added emoji icons and improved typography</li>
                    <li>• Hover effects with scale transforms and shadow changes</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
